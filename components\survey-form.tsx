"use client"

import { submitSur<PERSON> } from "@/app/actions"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import type React from "react"
import { useState } from "react"

export function SurveyForm() {
  const [selectedChoice1, setSelectedChoice1] = useState<string>("")
  const [selectedChoice2, setSelectedChoice2] = useState<string>("")
  const [vacationText, setVacationText] = useState("")
  const [name, setName] = useState("")
  const [phone, setPhone] = useState("")
  const [birthDate, setBirthDate] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      await submitSurvey({
        choice1: selectedChoice1,
        choice2: selectedChoice2,
        perfectVacation: vacationText,
        name,
        phone,
        birthDate,
      })
      setSubmitted(true)
    } catch (error) {
      console.error("Survey submission error:", error)
      alert("שגיאה בשליחת הסקר. אנא נסה שוב.")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitted) {
    return (
      <Card className="shadow-xl">
        <CardHeader className="text-center space-y-2 bg-primary text-primary-foreground rounded-t-lg">
          <CardTitle className="text-3xl">תודה רבה!</CardTitle>
          <CardDescription className="text-primary-foreground/90">הסקר שלך נשלח בהצלחה</CardDescription>
        </CardHeader>
        <CardContent className="pt-6 text-center">
          <p className="text-lg text-muted-foreground">אנו מעריכים את הזמן שהקדשת לשיתוף ההעדפות שלך איתנו. אנו ניצור קשר עם הזוכה.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-xl pt-0">
      <CardHeader className="text-center space-y-4 bg-primary text-primary-foreground rounded-t-lg pb-8 relative">
        <div className="flex justify-between items-center pt-4">
          <button
          type="button"
          className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
          aria-label="נגישות"
        >
          <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM8 7a1 1 0 0 0-1 1v5.5a1 1 0 0 0 1 1h1v6a1.5 1.5 0 0 0 3 0v-6h1v6a1.5 1.5 0 0 0 3 0v-6h1a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1H8z" />
          </svg>
        </button>
          <img src="/images/design-mode/IMG_1293.png" alt="לוגו חבר" className="h-16 w-auto" />
        <button
          type="button"
          className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
          aria-label="נגישות"
        >
          <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM8 7a1 1 0 0 0-1 1v5.5a1 1 0 0 0 1 1h1v6a1.5 1.5 0 0 0 3 0v-6h1v6a1.5 1.5 0 0 0 3 0v-6h1a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1H8z" />
          </svg>
        </button>
        </div>
        <CardTitle className="text-3xl font-bold">הגרלת חופשה זוגית - ילידי אוקטובר</CardTitle>
        <CardDescription className="text-primary-foreground/90 text-base max-w-2xl mx-auto">
          עזור לנו להבין טוב יותר את ההעדפות שלך! ענה על כמה שאלות קצרות על חווית החופשה האידיאלית שלך. <span className="font-bold">ואולי תזכה בחופשה החלומית שבחרת</span> בדיוק בזמן ליום ההולדת שלך.
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-8">
        <form onSubmit={handleSubmit} className="space-y-10">
          <div className="space-y-4">
            <h3 className="text-2xl font-semibold text-center">איזה סוג חופשה תעדיף עבור חגיגת היומולדת המושלמת?</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <button
                type="button"
                onClick={() => setSelectedChoice1("deadSea")}
                className={`group relative overflow-hidden rounded-lg border-2 transition-all ${
                  selectedChoice1 === "deadSea"
                    ? "border-primary ring-4 ring-primary/20 shadow-lg"
                    : "border-border hover:border-primary/50 hover:shadow-md"
                }`}
              >
                <div className="aspect-video overflow-hidden">
                  <img
                    src="/dead-sea.png"
                    alt="חופשה ביום המלח"
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-4 bg-card">
                  <p className="font-semibold text-lg mb-1">גלמפינג חלומי בים המלח</p>
                  <p className="text-sm text-muted-foreground">שילוב מיוחד של נוף עוצר נשימה, שפע של אטרקציות ופעילויות, יחד עם חווית שינה באוהל מפנק, ממוזג ומאובזר בכל מה שצריך</p>
                </div>
                {selectedChoice1 === "deadSea" && (
                  <div className="absolute top-3 left-3 bg-primary text-primary-foreground rounded-full p-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </button>

              <button
                type="button"
                onClick={() => setSelectedChoice1("cabin")}
                className={`group relative overflow-hidden rounded-lg border-2 transition-all ${
                  selectedChoice1 === "cabin"
                    ? "border-primary ring-4 ring-primary/20 shadow-lg"
                    : "border-border hover:border-primary/50 hover:shadow-md"
                }`}
              >
                <div className="aspect-video overflow-hidden">
                  <img
                    src="/cabin.png"
                    alt="צימר בצפון"
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-4 bg-card">
                  <p className="font-semibold text-lg mb-1">צימר מהמם בצפון</p>
                  <p className="text-sm text-muted-foreground">בריחה לבקתות הרריות שקטות ואוויר צח</p>
                </div>
                {selectedChoice1 === "cabin" && (
                  <div className="absolute top-3 left-3 bg-primary text-primary-foreground rounded-full p-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </button>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-2xl font-semibold text-center">איזה ארוחת ערב תהיה הדובדבן שבקצפת?</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <button
                type="button"
                onClick={() => setSelectedChoice2("restaurant")}
                className={`group relative overflow-hidden rounded-lg border-2 transition-all ${
                  selectedChoice2 === "restaurant"
                    ? "border-primary ring-4 ring-primary/20 shadow-lg"
                    : "border-border hover:border-primary/50 hover:shadow-md"
                }`}
              >
                <div className="aspect-video overflow-hidden">
                  <img
                    src="/restaurant.png"
                    alt="מסעדה"
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-4 bg-card">
                  <p className="font-semibold text-lg mb-1">מסעדה זוגית</p>
                  <p className="text-sm text-muted-foreground">ללכת למסעדה בערב, הבילוי המושלם לזוג</p>
                </div>
                {selectedChoice2 === "restaurant" && (
                  <div className="absolute top-3 left-3 bg-primary text-primary-foreground rounded-full p-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </button>

              <button
                type="button"
                onClick={() => setSelectedChoice2("cooking")}
                className={`group relative overflow-hidden rounded-lg border-2 transition-all ${
                  selectedChoice2 === "cooking"
                    ? "border-primary ring-4 ring-primary/20 shadow-lg"
                    : "border-border hover:border-primary/50 hover:shadow-md"
                }`}
              >
                <div className="aspect-video overflow-hidden">
                  <img
                    src="/cooking.png"
                    alt="בישול ביחד"
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-4 bg-card">
                  <p className="font-semibold text-lg mb-1">בישול ארוחה ביחד</p>
                  <p className="text-sm text-muted-foreground">בילוי זמן איכות עם הפרטנר בהכנת הארוחה המושלמת</p>
                </div>
                {selectedChoice2 === "cooking" && (
                  <div className="absolute top-3 left-3 bg-primary text-primary-foreground rounded-full p-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </button>
            </div>
          </div>

          <div className="space-y-3">
            <Label htmlFor="vacation" className="text-lg font-semibold">
              תאר את החופשה המושלמת שלך ואולי תזכה בה:
            </Label>
            <Textarea
              id="vacation"
              value={vacationText}
              onChange={(e) => setVacationText(e.target.value)}
              required
              rows={5}
              className="resize-none text-base"
              placeholder="ספר לנו על חופשת החלומות שלך..."
            />
          </div>

          <div className="space-y-6 pt-6 border-t">
            <h3 className="text-2xl font-semibold text-center">פרטים אישיים</h3>

            <div className="space-y-3">
              <Label htmlFor="name" className="text-lg font-semibold">
                שם מלא:
              </Label>
              <Input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="text-base"
                placeholder="הכנס את שמך המלא"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="phone" className="text-lg font-semibold">
                טלפון:
              </Label>
              <Input
                id="phone"
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                required
                className="text-base"
                placeholder="הכנס מספר טלפון"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="birthDate" className="text-lg font-semibold">
                תאריך לידה:
              </Label>
              <Input
                id="birthDate"
                type="date"
                value={birthDate}
                onChange={(e) => setBirthDate(e.target.value)}
                required
                className="text-base"
              />
            </div>
          </div>

          <div className="text-center pt-4">
            <Button
              type="submit"
              size="lg"
              disabled={
                !selectedChoice1 || !selectedChoice2 || !vacationText || !name || !phone || !birthDate || isSubmitting
              }
              className="px-12 text-lg"
            >
              {isSubmitting ? "שולח..." : "שלח סקר"}
            </Button>
          </div>
        </form>

        <div className="mt-12 pt-8 border-t space-y-4">
          <div className="flex flex-wrap justify-center gap-6 text-sm">
            <a href="#" className="text-primary hover:underline">
              מדיניות פרטיות
            </a>
            <a href="#" className="text-primary hover:underline">
              תנאי שימוש
            </a>
            <a href="#" className="text-primary hover:underline">
              צור קשר
            </a>
            <a href="#" className="text-primary hover:underline">
              אודות
            </a>
          </div>
          <p className="text-center text-sm text-muted-foreground">
            © {new Date().getFullYear()} כל הזכויות שמורות לחבר
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
