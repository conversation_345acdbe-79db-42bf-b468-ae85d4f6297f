-- Create survey_responses table
create table if not exists public.survey_responses (
  id uuid primary key default gen_random_uuid(),
  choice1 text not null,
  choice2 text not null,
  perfect_vacation text not null,
  created_at timestamp with time zone default now()
);

-- Enable RLS
alter table public.survey_responses enable row level security;

-- Allow anyone to insert survey responses (public survey)
create policy "Anyone can submit survey responses"
  on public.survey_responses
  for insert
  with check (true);

-- Allow anyone to view survey responses (optional - remove if you want responses private)
create policy "Anyone can view survey responses"
  on public.survey_responses
  for select
  using (true);
