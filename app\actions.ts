"use server"

import { createClient } from "@/lib/supabase/server"

interface SurveyData {
  choice1: string
  choice2: string
  perfectVacation: string
  name: string
  phone: string
  birthDate: string
}

export async function submitSurvey(data: SurveyData) {
  try {
    const supabase = await createClient()

    const { error } = await supabase.from("survey_responses").insert({
      choice1: data.choice1,
      choice2: data.choice2,
      perfect_vacation: data.perfectVacation,
      name: data.name,
      phone: data.phone,
      birth_date: data.birthDate,
    })

    if (error) {
      console.error("Error saving survey to database:", error)
      throw new Error("Failed to submit survey")
    }

    return { success: true }
  } catch (error) {
    console.error("Error submitting survey:", error)
    throw new Error("Failed to submit survey")
  }
}
